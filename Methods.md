# Methods: Equity Bias Analysis Across Biomedical Datasets

## Overview

We conducted comprehensive equity bias analyses across three major biomedical research domains to assess representation disparities by ancestry and sex. This analysis included neuropsychiatric disorders (PsychAD), cancer research (HTAN), and cell atlas studies (HCA).

## Data Sources

- **PsychAD**: psychAD media dataset (n=1,494) spanning neuropsychiatric disorders
- **HTAN**: Human Tumor Atlas Network dataset (n=1,959) covering multiple cancer types
- **HCA**: Human Cell Atlas dataset (n=10,125) from 15 tissue types

## Data Post-Processing

### Ancestry Standardization

All datasets were standardized to consistent ancestry categories to enable cross-dataset comparison:

**Target Categories**: African, Asian, European, Latino, Other, Unknown

**PsychAD Mapping**:
- AFR → African
- AMR → Latino
- EAS, SAS, EAS_SAS → Asian (combined East/South Asian)
- EUR → European
- Unknown/NA → Unknown
- Other reported values → Other

**HTAN Mapping** (ethnicity takes precedence):
- Hispanic/Latino ethnicity → Latino (regardless of race)
- White (non-Latino) → European
- Black/African American → African
- Asian → Asian
- Not reported/Unknown → Unknown
- Other → Other

**HCA Mapping**:
- european → European
- african → African
- asian → Asian
- hispanic or latino → Latino
- mixed → Other
- unknown/not provided/NA → Unknown

### Sex/Gender Standardization

All datasets standardized to: female, male, NA

**PsychAD**: Used 'sex' column (no missing values)
**HTAN**: Mapped 'Gender' column (Female/Male → female/male, Unknown → NA)
**HCA**: Processed 'donor_organism.sex' (unknown/invalid entries → NA)

### Disease/Condition Processing

**PsychAD**: 
- Combined cross-disorder and single diagnosis variables
- Created unified categories: AD, SCZ, DLBD, Vascular, BD, Tau, PD, FTD, Dementia
- Added Control category for samples without any listed diseases

**HTAN**:
- Extracted cancer types from Primary Diagnosis field
- Standardized to: Breast, Lung, Colorectal, Pancreas, Skin, Ovary, Liver, etc.

**HCA**:
- Used tissue sheet names as primary classification
- 15 tissue types: Adipose, Breast, Development, Eye, Gut, Heart, Immune, Kidney, Liver, Lung, Musculoskeletal, Oral/craniofacial, Pancreas, Reproduction, Skin

## Statistical Methods

### Descriptive Statistics
- Frequency distributions and percentages for all categorical variables
- Cross-tabulations for ancestry × sex intersections
- Missing data patterns by demographic groups

### Hypothesis Testing
- **Chi-square tests** for ancestry distribution vs. expected equal representation
- **Chi-square tests** for gender balance within each ancestry group (minimum n=10)
- Significance level: α = 0.05

### Equity Metrics
- **Representation ratios**: Actual vs. expected equal representation
- **Equity index**: Min(ratio, 1/ratio) for symmetric measure
- **Simpson's Diversity Index**: 1 - Σ(pi²) where pi = proportion of group i
- **Shannon's Diversity Index**: -Σ(pi × ln(pi))

### Missing Data Analysis
- Calculated missing data percentages by ancestry for key clinical variables
- Assessed differential missingness patterns across demographic groups

### Intersectional Analysis
- Examined representation at ancestry × sex intersections
- Identified underrepresented groups (<5% of total sample)
- Created heatmaps for visual assessment of intersectional disparities

## Software and Packages

**R version**: 4.x
**Key packages**: ggplot2, dplyr, tidyr, stringr, scales, readxl, car

## Visualization Standards

- Consistent color palettes across all datasets
- Ancestry colors: African (#E31A1C), Latino (#FF7F00), Asian (#1F78B4), European (#6A3D9A), Unknown (#CAB2D6), Other (#FDBF6F)
- Sex colors: female (#E69F00), male (#56B4E9)
- Standardized plot themes and font sizes

## Quality Control

- Comprehensive logging of all statistical outputs
- Automated capture of test statistics and p-values
- Cross-validation of ancestry mapping consistency
- Verification of sample size requirements for statistical tests

## Reproducibility

- All analyses scripted in R with version control
- Standardized file naming conventions
- Complete statistical logs saved for each dataset
- R workspace files preserved for further analysis
