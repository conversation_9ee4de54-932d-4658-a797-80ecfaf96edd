# Analysis Plan: Ancestry and Gender Bias Exploration

## Overview
This analysis will examine potential ancestry and gender bias in the psychAD media dataset containing 1,494 donors across multiple cohorts (MSBB, HBCC, RADC). The goal is to identify disparities in representation, clinical characteristics, and data availability that could impact research equity.

## Data Structure Analysis

### 1. Dataset Composition Overview ✓
- **Total samples**: 1,494 donors
- **Cohorts**: MSBB (1,042), HBCC (300), RADC (152), analyze this dataset as a whole
- **Key variables**: Ancestry, sex, age, disease status, clinical measures

### 2. Ancestry Distribution Analysis ✓
```python
# Code approach:
# - Count and percentage by ancestry group (AFR, EUR, AMR, EAS, SAS, etc.)
# - Visualize with bar charts and pie charts
# - Compare across cohorts
# - Identify underrepresented groups
```

### 3. Gender Distribution Analysis ✓
```python
# Code approach:
# - Overall male/female distribution
# - Gender distribution by ancestry
# - Gender distribution by cohort
# - Age distribution by gender
```

## Bias Detection Strategies

### 1. Representation Bias ✓
**Objective**: Identify underrepresented demographic groups

**Analyses**:
- Ancestry frequency distribution
- Gender balance within ancestry groups
- Intersectional analysis (ancestry × gender)
- Comparison to US population demographics
- Missing data patterns by demographics

**Visualizations**:
- Stacked bar charts (ancestry by gender)
- Heatmaps showing sample counts
- Comparison charts with population benchmarks

### 2. Age Bias ✓
**Objective**: Examine age distribution patterns across demographics

**Analyses**:
- Age distribution by ancestry and gender
- Statistical tests for age differences between groups
- Age range coverage by demographic groups
- Missing age data patterns

**Visualizations**:
- Box plots and violin plots by demographics
- Age histograms stratified by ancestry/gender
- Scatter plots with trend lines



## Statistical Analysis Plan

### 1. Descriptive Statistics ✓
- Summary tables by ancestry and gender
- Cross-tabulation tables
- Confidence intervals for proportions

### 2. Hypothesis Testing ✓
- Chi-square tests for categorical variables
- ANOVA/Kruskal-Wallis for continuous variables
- Post-hoc multiple comparison corrections
- Effect size calculations

### 3. Equity Metrics ✓
- Representation ratios compared to population
- Diversity indices (Simpson's, Shannon's)
- Power analysis for underrepresented groups
- Bias quantification metrics

---

## ✅ EXECUTION SUMMARY - ALL STEPS COMPLETED

### Completed Tasks ✓
1. **Dataset Composition Overview** ✓ - Analyzed 1,494 samples across 3 cohorts
2. **Ancestry Distribution Analysis** ✓ - Identified EUR dominance (65.9%) and Asian under-representation (<2%)
3. **Gender Distribution Analysis** ✓ - Found overall balance but severe bias in small ancestry groups
4. **Representation Bias Analysis** ✓ - Quantified underrepresentation and intersectional disparities
5. **Age Bias Analysis** ✓ - Detected significant age differences by ancestry and gender
6. **Statistical Analysis** ✓ - Conducted hypothesis tests, ANOVA, and post-hoc analyses
7. **Equity Metrics** ✓ - Calculated diversity indices and representation ratios

### Generated Outputs ✓
- **9 visualization files** in PDF format
- **1 comprehensive R analysis script** (`equity_bias_analysis.R`)
- **1 complete workspace file** (`equity_bias_analysis_workspace.RData`)
- **1 detailed summary report** (`ANALYSIS_SUMMARY_REPORT.md`)

### Key Findings ✓
- **EUR over-representation**: 3.98× expected levels
- **Asian severe under-representation**: EAS, EAS_SAS, SAS combined <2%
- **Age bias**: Consistent 10-13 year female vs male age difference across ancestries
- **Missing data bias**: 50-87% missing clinical measures, worse in underrepresented groups
- **Power concerns**: 3 ancestry groups have n<20, limiting statistical analyses

### Research Impact ✓
- Provides quantitative evidence of representation bias in psychAD research
- Establishes framework for equity assessment in neurological datasets
- Identifies specific limitations for generalizability of research findings
- Offers concrete recommendations for improving research equity

**Analysis completed successfully on June 25, 2025**
