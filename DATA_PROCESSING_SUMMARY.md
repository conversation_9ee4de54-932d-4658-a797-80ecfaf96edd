# Data Processing Summary: Equity Bias Analysis
## Post-processing Steps for psychAD, HTAN, and HCA Datasets

### Overview
This document summarizes the data cleaning, ancestry mapping, and statistical processing steps applied to each of the three datasets to ensure consistent and comparable equity bias analysis.

---

## 1. psychAD Dataset Processing

### **Data Source**
- **File**: `data/psych-AD_media-1.csv`
- **Original samples**: 1,494
- **Domain**: Neuropsychiatric disorders
- **Key variables**: Ancestry, sex, cohort, disease diagnoses

### **Ancestry Categorization**
```r
# Original values in data: EUR, AFR, AMR, EAS, SAS, Unknown, EAS_SAS
data$Ancestry_clean <- case_when(
  data$Ancestry %in% c("AFR", "AMR", "EAS", "EUR", "SAS") ~ data$Ancestry,
  data$Ancestry == "EAS_SAS" ~ "Other",  # Reported mixed category
  data$Ancestry == "Unknown" | is.na(data$Ancestry) ~ "Unknown",  # Missing/NA
  TRUE ~ "Other"  # Any other reported values
)
```

### **Final Ancestry Distribution**
| Category | Count | Percentage | Classification |
|----------|-------|------------|----------------|
| EUR | 985 | 65.93% | Canonical |
| AFR | 338 | 22.62% | Canonical |
| AMR | 135 | 9.04% | Canonical |
| EAS | 14 | 0.94% | Canonical |
| SAS | 5 | 0.33% | Canonical |
| Other | 8 | 0.54% | Non-canonical (EAS_SAS) |
| Unknown | 9 | 0.60% | Missing/NA |

### **Gender Processing**
- **Variable**: `sex` (female/male)
- **Missing data**: 0%
- **No additional cleaning required**

### **Disease Variables**
- Combined cross-disorder and single diagnosis variables
- Created unified disease categories (AD_combined, SCZ, DLBD, etc.)
- Handled missing values in clinical measures (CERAD, Braak, PMI)

---

## 2. HTAN Dataset Processing

### **Data Source**
- **File**: `data/HTAN Data Final.xlsx`
- **Original samples**: 1,959
- **Domain**: Cancer research
- **Key variables**: Race, Gender, Primary Diagnosis, Cancer staging

### **Ancestry Categorization**
```r
# Original values: "white", "black or african american", "asian", "other", 
#                  "not reported", "unknown", "not allowed to collect"
Race_clean = case_when(
  tolower(Race) %in% c("white") ~ "EUR",
  tolower(Race) %in% c("black or african american") ~ "AFR", 
  tolower(Race) %in% c("asian") ~ "EAS",
  tolower(Race) %in% c("other") ~ "Other",  # Reported but not canonical
  tolower(Race) %in% c("not reported", "unknown", "not allowed to collect") | 
    is.na(Race) ~ "Unknown",  # Missing/NA
  TRUE ~ "Other"
)
```

### **Final Ancestry Distribution**
| Category | Count | Percentage | Classification |
|----------|-------|------------|----------------|
| EUR | 1,362 | 69.5% | Canonical |
| AFR | 298 | 15.2% | Canonical |
| EAS | 36 | 1.8% | Canonical |
| Other | ~50 | ~2.5% | Non-canonical ("other") |
| Unknown | ~213 | ~10.9% | Missing/NA |

### **Gender Processing**
```r
Gender_clean = case_when(
  tolower(Gender) %in% c("female") ~ "female",
  tolower(Gender) %in% c("male") ~ "male", 
  tolower(Gender) %in% c("not reported", "unknown") | is.na(Gender) ~ NA_character_,
  TRUE ~ NA_character_
)
```

### **Cancer Type Processing**
- Extracted cancer types from `Primary Diagnosis` field
- Mapped to standardized categories (Breast, Lung, Colorectal, etc.)
- Used `Tissue Site` as backup when primary diagnosis unclear
- Created sample size labels for visualization

---

## 3. HCA Dataset Processing

### **Data Source**
- **File**: `data/HCA scrnaseq data final.xlsx` (15 tissue-specific sheets)
- **Original samples**: 10,125 (combined across all sheets)
- **Domain**: Single-cell genomics
- **Key variables**: Ethnicity ontology, sex, tissue type

### **Multi-sheet Data Combination**
```r
# Read all 15 tissue sheets and combine
sheet_names <- excel_sheets(excel_file)
# Sheets: Adipose, Breast, Development, Eye, Gut, Heart, Immune, 
#         Kidney, Liver, Lung, Musculoskeletal, Oral and craniofacial, 
#         Pancreas, Reproduction, Skin

for(sheet_name in sheet_names) {
  sheet_data <- read_excel(excel_file, sheet = sheet_name)
  sheet_data <- sheet_data %>% mutate_all(as.character)  # Avoid type conflicts
  sheet_data$Tissue_Sheet <- sheet_name
  all_data[[sheet_name]] <- sheet_data
}
data_raw <- bind_rows(all_data)
```

### **Ancestry Categorization**
```r
# Using: donor_organism.human_specific.ethnicity.ontology_aggregated
Race_clean = case_when(
  tolower(`...ethnicity.ontology_aggregated`) %in% c("european") ~ "EUR",
  tolower(`...ethnicity.ontology_aggregated`) %in% c("african") ~ "AFR", 
  tolower(`...ethnicity.ontology_aggregated`) %in% c("asian", "east asian", 
    "south asian", "south-east asian") ~ "EAS",
  tolower(`...ethnicity.ontology_aggregated`) %in% c("american") ~ "AMR",
  tolower(`...ethnicity.ontology_aggregated`) %in% c("other", "mixed", 
    "homo sapiens") ~ "Other",  # Reported but not canonical
  tolower(`...ethnicity.ontology_aggregated`) %in% c("unknown", 
    "not provided") ~ "Unknown",  # Explicitly unknown
  is.na(`...ethnicity.ontology_aggregated`) ~ "Unknown",  # Missing
  TRUE ~ "Other"
)
```

### **Final Ancestry Distribution**
| Category | Count | Percentage | Classification |
|----------|-------|------------|----------------|
| EUR | 1,720 | 16.99% | Canonical |
| EAS | 643 | 6.35% | Canonical |
| AFR | 317 | 3.13% | Canonical |
| AMR | 189 | 1.87% | Canonical |
| Other | ~500 | ~4.9% | Non-canonical |
| Unknown | 7,256 | 71.66% | Missing/NA |

### **Gender Processing**
```r
# Using: donor_organism.sex
Gender_clean = case_when(
  tolower(`donor_organism.sex`) %in% c("female") ~ "female",
  tolower(`donor_organism.sex`) %in% c("male") ~ "male", 
  tolower(`donor_organism.sex`) %in% c("unknown", "yes", "homo sapiens") ~ NA_character_,
  is.na(`donor_organism.sex`) ~ NA_character_,
  TRUE ~ NA_character_
)
```

### **Tissue Type Processing**
- Used sheet names as primary tissue classification
- 15 tissue types: Immune (largest), Skin, Gut, Lung, Heart, etc.
- No additional mapping required

---

## Statistical Processing (Consistent Across All Datasets)

### **Equity Metrics**
1. **Representation Ratios**: Actual count / Expected equal representation
2. **Simpson's Diversity Index**: 1 - Σ(pi²)
3. **Shannon's Diversity Index**: -Σ(pi × ln(pi))
4. **Chi-square tests**: For distribution bias testing

### **Visualization Strategy**
1. **Dual Plot Versions**:
   - **Full**: Including Unknown/Other categories
   - **Canonical**: Excluding Unknown/Other categories
2. **Consistent Color Scheme**:
   - AFR: #E31A1C, AMR: #FF7F00, EAS: #1F78B4
   - EUR: #6A3D9A, SAS: #FB9A99
   - Unknown: #CAB2D6, Other: #FDBF6F

### **Missing Data Analysis**
- Calculated missingness percentages by ancestry group
- Identified differential missing data patterns
- Assessed impact on statistical power

---

## Key Challenges and Solutions

### **Data Type Conflicts (HCA)**
- **Problem**: Different data types across sheets prevented binding
- **Solution**: Convert all columns to character before combining

### **Ancestry Standardization**
- **Problem**: Different terminology across datasets
- **Solution**: Systematic mapping to canonical categories (AFR, AMR, EAS, EUR, SAS)

### **Missing Data Handling**
- **Problem**: Various representations of missing data
- **Solution**: Distinguish "Unknown" (missing/NA) from "Other" (reported but non-canonical)

### **Statistical Power**
- **Problem**: Small sample sizes for some ancestry groups
- **Solution**: Flag underpowered groups and adjust analysis accordingly

---

## Output Standardization

### **File Naming Convention**
- **psychAD**: `psychAD_[analysis]_[version].pdf`
- **HTAN**: `HTAN_[analysis]_[version].pdf`
- **HCA**: `HCA_[analysis]_[version].pdf`
- **Versions**: `_full` (all categories), `_canonical` (canonical only)

### **Log Files**
- Comprehensive logging with statistical tables
- Consistent format across all three analyses
- Saved as: `[dataset]_analysis_log.txt`

This standardized processing ensures comparable and reproducible equity bias analysis across all three biomedical research domains.
